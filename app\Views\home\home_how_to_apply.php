<?= $this->extend('templates/home_template') ?>

<?= $this->section('content') ?>

<!-- Hero Section -->
<section class="hero-section bg-gradient-primary text-white py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-3">How to Apply for Government Positions</h1>
                <p class="lead mb-4">Complete step-by-step guide to applying for government positions through the DERS system with AI-powered document processing</p>
                <div class="d-flex gap-3 flex-wrap">
                    <a href="<?= base_url('applicant/register') ?>" class="btn btn-yellow btn-lg">
                        <i class="fas fa-user-plus me-2"></i>Start Your Application
                    </a>
                    <a href="<?= base_url('jobs') ?>" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-search me-2"></i>Browse Jobs
                    </a>
                </div>
                <div class="mt-4">
                    <small class="text-light opacity-75">
                        <i class="fas fa-robot me-2"></i>Powered by Advanced AI for Document Analysis & Processing
                    </small>
                </div>
            </div>
            <div class="col-lg-4 text-center">
                <i class="fas fa-clipboard-list" style="font-size: 8rem; opacity: 0.3;"></i>
            </div>
        </div>
    </div>
</section>

<!-- Quick Overview -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-12 text-center mb-5">
                <h2 class="h1 fw-bold text-navy">Application Process Overview</h2>
                <p class="lead text-muted">Follow these simple steps to apply for government positions</p>
            </div>
        </div>
        <div class="row g-4">
            <div class="col-md-3">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="bg-red text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                            <span class="fw-bold fs-4">1</span>
                        </div>
                        <h5 class="card-title">Create Account</h5>
                        <p class="card-text text-muted">Register with your email and create a secure password</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="bg-red text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                            <span class="fw-bold fs-4">2</span>
                        </div>
                        <h5 class="card-title">Complete Profile</h5>
                        <p class="card-text text-muted">Fill in your personal information and upload documents</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="bg-red text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                            <span class="fw-bold fs-4">3</span>
                        </div>
                        <h5 class="card-title">Browse Jobs</h5>
                        <p class="card-text text-muted">Search and find positions that match your qualifications</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="bg-red text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                            <span class="fw-bold fs-4">4</span>
                        </div>
                        <h5 class="card-title">Submit Application</h5>
                        <p class="card-text text-muted">Apply with one click and track your application status</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Detailed Steps -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <h2 class="h1 fw-bold text-navy mb-5 text-center">Detailed Application Guide</h2>
                
                <!-- Step 1: Getting Started -->
                <div class="card mb-5 border-0 shadow">
                    <div class="card-header bg-red text-white">
                        <h3 class="card-title mb-0"><i class="fas fa-play-circle me-2"></i>Step 1: Getting Started</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-lg-8">
                                <h4>Access the DERS System</h4>
                                <p>Start your application journey by visiting the DERS homepage. The system features an intuitive interface designed for easy navigation and efficient application processing.</p>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success me-2"></i><strong>Website URL:</strong> https://ders.dakoiims.com/ders/</li>
                                    <li><i class="fas fa-check text-success me-2"></i><strong>System Name:</strong> DERS (Dakoii Echad Recruitment & Selection System)</li>
                                    <li><i class="fas fa-check text-success me-2"></i><strong>Browser Requirements:</strong> Modern web browsers (Chrome, Firefox, Safari, Edge)</li>
                                </ul>

                                <h5>System Features</h5>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-robot text-primary me-2"></i><strong>AI-Powered Document Processing:</strong> Advanced AI analyzes your documents</li>
                                    <li><i class="fas fa-shield-alt text-success me-2"></i><strong>Secure File Storage:</strong> Your documents are safely stored and encrypted</li>
                                    <li><i class="fas fa-mobile-alt text-info me-2"></i><strong>Mobile Responsive:</strong> Apply from any device, anywhere</li>
                                    <li><i class="fas fa-clock text-warning me-2"></i><strong>Real-time Processing:</strong> Instant feedback and status updates</li>
                                </ul>

                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>Before You Begin:</strong> Ensure you have a valid email address, prepare your supporting documents (CV, certificates, transcripts), and download the official RS 3.2 Application Form as it is required.
                                </div>
                            </div>
                            <div class="col-lg-4">
                                <img src="<?= base_url('public/assets/how_to/home_landing_page.png') ?>" alt="DERS Homepage" class="img-fluid rounded shadow">
                                <p class="text-muted text-center mt-2"><small>DERS Homepage - Your starting point</small></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 2: Account Registration -->
                <div class="card mb-5 border-0 shadow">
                    <div class="card-header bg-red text-white">
                        <h3 class="card-title mb-0"><i class="fas fa-user-plus me-2"></i>Step 2: Account Registration</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-lg-8">
                                <h4>Create Your Account</h4>
                                <p>Follow these steps to register for a new account:</p>
                                
                                <h5>2.1 Access Registration Page</h5>
                                <ol>
                                    <li>Click on <strong>"Apply"</strong> in the main navigation menu</li>
                                    <li>Select <strong>"Create Account"</strong> or <strong>"Register"</strong></li>
                                </ol>
                                
                                <h5>2.2 Fill Registration Form</h5>
                                <p><strong>Required Information:</strong></p>
                                <ul>
                                    <li><i class="fas fa-asterisk text-danger me-1" style="font-size: 0.6rem;"></i><strong>First Name</strong> (minimum 2 characters)</li>
                                    <li><i class="fas fa-asterisk text-danger me-1" style="font-size: 0.6rem;"></i><strong>Last Name</strong> (minimum 2 characters)</li>
                                    <li><i class="fas fa-asterisk text-danger me-1" style="font-size: 0.6rem;"></i><strong>Email Address</strong> (must be unique and valid)</li>
                                    <li><i class="fas fa-asterisk text-danger me-1" style="font-size: 0.6rem;"></i><strong>Password</strong> (minimum 4 characters)</li>
                                    <li><i class="fas fa-asterisk text-danger me-1" style="font-size: 0.6rem;"></i><strong>Confirm Password</strong> (must match password)</li>
                                </ul>
                                
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>Email Validation:</strong> The system will check if your email is already registered. You'll see real-time validation feedback.
                                </div>
                            </div>
                            <div class="col-lg-4">
                                <img src="<?= base_url('public/assets/how_to/home_applicant_create_account.png') ?>" alt="Registration Form" class="img-fluid rounded shadow">
                                <p class="text-muted text-center mt-2"><small>Registration form with required fields</small></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 3: Account Activation -->
                <div class="card mb-5 border-0 shadow">
                    <div class="card-header bg-red text-white">
                        <h3 class="card-title mb-0"><i class="fas fa-envelope-open me-2"></i>Step 3: Account Activation</h3>
                    </div>
                    <div class="card-body">
                        <h4>Email Activation Process</h4>
                        <div class="row">
                            <div class="col-lg-6">
                                <ol>
                                    <li><strong>Check Your Email:</strong> Look for an email from "DERS System" (<EMAIL>)</li>
                                    <li><strong>Subject Line:</strong> "Activate Your DERS Account"</li>
                                    <li><strong>Click Activation Link:</strong> Click the activation link in the email</li>
                                    <li><strong>Confirmation:</strong> You'll be redirected to the login page with a success message</li>
                                    <li><strong>Account Status:</strong> Your account is now active and ready to use</li>
                                </ol>
                            </div>
                            <div class="col-lg-6">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle me-2"></i>Important Notes:</h6>
                                    <ul class="mb-0">
                                        <li>Activation emails may take a few minutes to arrive</li>
                                        <li>Check your spam/junk folder if you don't see the email</li>
                                        <li>The activation link is unique and can only be used once</li>
                                        <li>Contact support if you don't receive the activation email</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 4: Login Process -->
                <div class="card mb-5 border-0 shadow">
                    <div class="card-header bg-red text-white">
                        <h3 class="card-title mb-0"><i class="fas fa-sign-in-alt me-2"></i>Step 4: Login Process</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-lg-8">
                                <h4>Access Your Account</h4>

                                <h5>4.1 Access Login Page</h5>
                                <ol>
                                    <li>Go to the DERS homepage</li>
                                    <li>Click <strong>"Apply"</strong> in the navigation menu</li>
                                    <li>Select <strong>"Login"</strong> or go directly to the applicant login page</li>
                                </ol>

                                <h5>4.2 Enter Credentials</h5>
                                <ul>
                                    <li><strong>Email Address:</strong> Your registered email</li>
                                    <li><strong>Password:</strong> Your account password</li>
                                </ul>

                                <h5>4.3 Login Validation</h5>
                                <ul>
                                    <li>If credentials are correct, you'll be redirected to your dashboard</li>
                                    <li>If account is not activated, you'll see a warning message</li>
                                    <li>Invalid credentials will show an error message</li>
                                </ul>

                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle me-2"></i>
                                    <strong>Successful Login:</strong> Welcome message will appear and you'll be taken to your applicant dashboard
                                </div>
                            </div>
                            <div class="col-lg-4">
                                <img src="<?= base_url('public/assets/how_to/home_applicant_login.png') ?>" alt="Login Form" class="img-fluid rounded shadow">
                                <p class="text-muted text-center mt-2"><small>Applicant login page</small></p>

                                <div class="mt-3">
                                    <img src="<?= base_url('public/assets/how_to/applicant_login_successful_login.png') ?>" alt="Successful Login" class="img-fluid rounded shadow">
                                    <p class="text-muted text-center mt-2"><small>Successful login redirects to dashboard</small></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 5: Browse Jobs -->
                <div class="card mb-5 border-0 shadow">
                    <div class="card-header bg-red text-white">
                        <h3 class="card-title mb-0"><i class="fas fa-search me-2"></i>Step 5: Browse Available Positions</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-lg-8">
                                <h4>Find Job Opportunities</h4>

                                <h5>5.1 Access Job Listings</h5>
                                <ul>
                                    <li>Click <strong>"Jobs"</strong> in the main navigation</li>
                                    <li>Browse through available government positions</li>
                                    <li>Use filters to narrow down your search</li>
                                </ul>

                                <h5>5.2 Search and Filter Options</h5>
                                <ul>
                                    <li><strong>Organization:</strong> Filter by government department</li>
                                    <li><strong>Classification:</strong> Filter by position grade (PS11, PS13, etc.)</li>
                                    <li><strong>Location:</strong> Search by work location</li>
                                    <li><strong>Type:</strong> Internal (current employees) or External (public)</li>
                                    <li><strong>Search:</strong> Use keywords to find specific positions</li>
                                </ul>

                                <h5>5.3 Position Information</h5>
                                <p>Each position listing shows:</p>
                                <ul>
                                    <li>Job title and organization</li>
                                    <li>Location and salary range</li>
                                    <li>Application deadline</li>
                                    <li>Position type (Internal/External)</li>
                                    <li>Classification level</li>
                                </ul>

                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>Important:</strong> Internal positions are only visible if you work for the advertising organization. External positions are open to all qualified applicants.
                                </div>
                            </div>
                            <div class="col-lg-4">
                                <img src="<?= base_url('public/assets/how_to/home_jobs_page.png') ?>" alt="Job Listings" class="img-fluid rounded shadow">
                                <p class="text-muted text-center mt-2"><small>Public job listings page</small></p>

                                <div class="mt-3">
                                    <img src="<?= base_url('public/assets/how_to/applicant_job_openinings.png') ?>" alt="Applicant Job Openings" class="img-fluid rounded shadow">
                                    <p class="text-muted text-center mt-2"><small>Applicant portal job openings with filters</small></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 6: Applicant Dashboard -->
                <div class="card mb-5 border-0 shadow">
                    <div class="card-header bg-red text-white">
                        <h3 class="card-title mb-0"><i class="fas fa-tachometer-alt me-2"></i>Step 6: Applicant Dashboard Navigation</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-lg-8">
                                <h4>Understanding Your Dashboard</h4>
                                <p>After successful login, you'll be taken to your personalized applicant dashboard. This is your central hub for managing your profile, applications, and documents.</p>

                                <h5>Dashboard Features</h5>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-chart-pie text-primary me-2"></i><strong>Profile Completion Status:</strong> Shows percentage completed</li>
                                    <li><i class="fas fa-bolt text-warning me-2"></i><strong>Quick Actions:</strong> Direct links to important functions</li>
                                    <li><i class="fas fa-history text-info me-2"></i><strong>Recent Applications:</strong> Summary of your latest applications</li>
                                    <li><i class="fas fa-file-upload text-success me-2"></i><strong>File Upload Status:</strong> Indicates if you've uploaded required documents</li>
                                </ul>

                                <h5>Main Navigation Menu</h5>
                                <ol>
                                    <li><strong>Dashboard</strong> - Overview of your profile and applications</li>
                                    <li><strong>Job Openings</strong> - Browse available positions</li>
                                    <li><strong>Applications</strong> - Track your submitted applications</li>
                                    <li><strong>Profile</strong> - Manage your personal information and documents</li>
                                </ol>

                                <div class="alert alert-info">
                                    <i class="fas fa-lightbulb me-2"></i>
                                    <strong>Navigation Tip:</strong> The current page is highlighted in the navigation menu, and your name and email are displayed in the header for easy identification.
                                </div>
                            </div>
                            <div class="col-lg-4">
                                <img src="<?= base_url('public/assets/how_to/applicant_dashboard.png') ?>" alt="Applicant Dashboard" class="img-fluid rounded shadow">
                                <p class="text-muted text-center mt-2"><small>Your personalized applicant dashboard</small></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 7: Profile Requirements -->
                <div class="card mb-5 border-0 shadow">
                    <div class="card-header bg-red text-white">
                        <h3 class="card-title mb-0"><i class="fas fa-user-edit me-2"></i>Step 7: Complete Your Profile</h3>
                    </div>
                    <div class="card-body">
                        <h4>Profile Completion Requirements</h4>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Important:</strong> Your profile must be 100% complete with at least one file uploaded before you can apply for any position.
                        </div>

                        <div class="row">
                            <div class="col-lg-6">
                                <h5>Mandatory Profile Sections</h5>

                                <h6><i class="fas fa-user me-2"></i>Personal Information</h6>
                                <ul>
                                    <li>First Name and Last Name</li>
                                    <li>Gender (Male/Female)</li>
                                    <li>Date of Birth</li>
                                    <li>Contact Details (phone number)</li>
                                    <li>Email Address</li>
                                    <li>Location Address</li>
                                    <li>Place of Origin</li>
                                    <li>Citizenship</li>
                                </ul>

                                <h6><i class="fas fa-briefcase me-2"></i>Employment Information</h6>
                                <ul>
                                    <li>Current Employer</li>
                                    <li>Current Position</li>
                                    <li>Current Salary</li>
                                    <li>Public Servant Status (if applicable)</li>
                                    <li>Public Service File Number</li>
                                    <li>Employee Organization</li>
                                </ul>
                            </div>
                            <div class="col-lg-6">
                                <h6><i class="fas fa-id-card me-2"></i>Identification</h6>
                                <ul>
                                    <li>ID Numbers (passport, national ID, etc.)</li>
                                    <li>ID Photo Upload (JPG, JPEG, PNG - max 2MB)</li>
                                </ul>

                                <h6><i class="fas fa-users me-2"></i>Additional Information</h6>
                                <ul>
                                    <li>Referees (contact information)</li>
                                    <li>Marital Status</li>
                                    <li>Children information</li>
                                    <li>Any criminal convictions</li>
                                </ul>

                                <h6><i class="fas fa-upload me-2"></i>File Upload Requirements</h6>
                                <ul>
                                    <li><strong>Supported Types:</strong> PDF, JPG, JPEG, PNG</li>
                                    <li><strong>Maximum Size:</strong> 25MB per file</li>
                                    <li><strong>Processing Timeout:</strong> 4 minutes for large files</li>
                                    <li><strong>AI Processing:</strong> Advanced AI content analysis</li>
                                    <li><strong>Required:</strong> At least one file must be uploaded</li>
                                </ul>

                                <div class="alert alert-success">
                                    <i class="fas fa-robot me-2"></i>
                                    <strong>AI-Powered Processing:</strong> Your documents are automatically processed with advanced AI for full content analysis and text extraction.
                                </div>
                            </div>
                            <div class="col-lg-4">
                                <img src="<?= base_url('public/assets/how_to/applicant_profile_page.png') ?>" alt="Profile Page" class="img-fluid rounded shadow">
                                <p class="text-muted text-center mt-2"><small>Complete profile management interface</small></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 8: File Upload & AI Processing -->
                <div class="card mb-5 border-0 shadow">
                    <div class="card-header bg-red text-white">
                        <h3 class="card-title mb-0"><i class="fas fa-cloud-upload-alt me-2"></i>Step 8: File Upload & AI Processing</h3>
                    </div>
                    <div class="card-body">
                        <h4>Document Upload Process with AI Analysis</h4>
                        <p>The DERS system features advanced AI-powered document processing that automatically analyzes and extracts content from your uploaded files.</p>

                        <div class="row">
                            <div class="col-lg-8">
                                <h5>Step-by-Step Upload Process</h5>

                                <div class="mb-4">
                                    <h6>8.1 Access Upload Page</h6>
                                    <ol>
                                        <li>Go to Profile → Files → Upload New File</li>
                                        <li>Click "Upload New File" button</li>
                                        <li>You'll see the file upload interface</li>
                                    </ol>
                                </div>

                                <div class="mb-4">
                                    <h6>8.2 Select and Upload File</h6>
                                    <ol>
                                        <li>Choose file from your device (PDF)</li>
                                        <li>Provide file title and description</li>
                                        <li>Click "Upload File" to start the process</li>
                                        <li>File validation occurs (size, type, format)</li>
                                    </ol>
                                </div>

                                <div class="mb-4">
                                    <h6>8.3 AI Content Analysis</h6>
                                    <p>Once uploaded, the system automatically processes your document:</p>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-robot text-primary me-2"></i><strong>Automatic Processing:</strong> Advanced AI analyzes document content and processes the document</li>
                                        <li><i class="fas fa-search text-info me-2"></i><strong>Full Content Analysis:</strong> Complete document analysis</li>
                                        <li><i class="fas fa-file-alt text-success me-2"></i><strong>Text Extraction:</strong> All readable text is extracted and stored</li>
                                        <li><i class="fas fa-clock text-warning me-2"></i><strong>Processing Time:</strong> Large documents (20+ pages) may take 5+ minutes</li>
                                    </ul>
                                </div>

                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>AI Processing Note:</strong> The system prioritizes readability. Ensure that your documents are clear and readable. Blurred or low quality documents my cause processing errors and difficulty in analysis, thus may affect the outcome of your application profile. 
                                </div>
                            </div>
                            <div class="col-lg-4">
                                <div class="mb-3">
                                    <img src="<?= base_url('public/assets/how_to/applicant_profile_upload_file_one.png') ?>" alt="File Upload Step 1" class="img-fluid rounded shadow">
                                    <p class="text-muted text-center mt-2"><small>Step 1: File upload interface</small></p>
                                </div>

                                <div class="mb-3">
                                    <img src="<?= base_url('public/assets/how_to/applicant_profile_upload_file_two.png') ?>" alt="File Upload Step 2" class="img-fluid rounded shadow">
                                    <p class="text-muted text-center mt-2"><small>Step 2: File selection and details</small></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 9: Application Submission -->
                <div class="card mb-5 border-0 shadow">
                    <div class="card-header bg-red text-white">
                        <h3 class="card-title mb-0"><i class="fas fa-paper-plane me-2"></i>Step 9: Submit Your Application</h3>
                    </div>
                    <div class="card-body">
                        <h4>Detailed Application Submission Process</h4>
                        <p>Once your profile is complete and you've uploaded the required documents, you can apply for positions. The system uses AI to compile and analyze your application data.</p>

                        <div class="row">
                            <div class="col-lg-8">
                                <h5>Prerequisites</h5>
                                <p>Before applying for any position, ensure:</p>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success me-2"></i>Profile is 100% complete</li>
                                    <li><i class="fas fa-check text-success me-2"></i>At least one file uploaded</li>
                                    <li><i class="fas fa-check text-success me-2"></i>All mandatory fields filled</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Valid contact information provided</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Account is activated and verified</li>
                                </ul>

                                <h5>Step-by-Step Application Process</h5>

                                <div class="mb-4">
                                    <h6>9.1 Select Position</h6>
                                    <ol>
                                        <li>Browse available positions in Job Openings</li>
                                        <li>Click on position title to view details</li>
                                        <li>Review job requirements and deadline</li>
                                        <li>Click "Apply for this Position" button</li>
                                    </ol>
                                </div>

                                <div class="mb-4">
                                    <h6>9.2 Application Review Page</h6>
                                    <p>The system will display:</p>
                                    <ul>
                                        <li><strong>Position Summary:</strong> Confirm job details</li>
                                        <li><strong>Your Profile:</strong> Review your information</li>
                                        <li><strong>Document Check:</strong> Ensure all files are uploaded</li>
                                        <li><strong>Eligibility Verification:</strong> System checks requirements</li>
                                    </ul>
                                </div>

                                <div class="mb-4">
                                    <h6>9.3 AI Profile Compilation</h6>
                                    <p>The system automatically:</p>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-robot text-primary me-2"></i><strong>Profile Compilation:</strong> Your information is compiled into application format</li>
                                        <li><i class="fas fa-brain text-info me-2"></i><strong>AI Analysis:</strong> Profile analyzed and structured for submission</li>
                                        <li><i class="fas fa-copy text-success me-2"></i><strong>File Compilation:</strong> Documents compiled to application folder</li>
                                        <li><i class="fas fa-database text-warning me-2"></i><strong>Database Storage:</strong> Application saved in system database</li>
                                    </ul>
                                </div>

                                <div class="mb-4">
                                    <h6>9.4 Final Submission</h6>
                                    <ol>
                                        <li><strong>Review Application:</strong> Check all information is correct</li>
                                        <li><strong>Confirmation Dialog:</strong> Confirm you want to submit</li>
                                        <li><strong>Submit Application:</strong> Click final submission button</li>
                                        <li><strong>Processing:</strong> Application is processed and saved</li>
                                    </ol>
                                </div>

                                <div class="alert alert-success">
                                    <i class="fas fa-envelope me-2"></i>
                                    <strong>Email Confirmation:</strong> When HR confirms receiving your application, an email will be sent to your address with application details and reference number.
                                </div>
                            </div>
                            <div class="col-lg-4">
                                <div class="mb-3">
                                    <img src="<?= base_url('public/assets/how_to/applicant_job_openinings_position_details.png') ?>" alt="Position Details" class="img-fluid rounded shadow">
                                    <p class="text-muted text-center mt-2"><small>Step 1: Position details and apply button</small></p>
                                </div>

                                <div class="mb-3">
                                    <img src="<?= base_url('public/assets/how_to/applicant_job_openinings_application_submission_page_one.png') ?>" alt="Application Review" class="img-fluid rounded shadow">
                                    <p class="text-muted text-center mt-2"><small>Step 2: Application review page</small></p>
                                </div>

                                <div class="mb-3">
                                    <img src="<?= base_url('public/assets/how_to/applicant_job_openinings_application_submission_page_two_application_ready_for_submission.png') ?>" alt="Ready for Submission" class="img-fluid rounded shadow">
                                    <p class="text-muted text-center mt-2"><small>Step 3: Application ready for submission</small></p>
                                </div>

                                <div class="mb-3">
                                    <img src="<?= base_url('public/assets/how_to/applicant_job_openinings_confirm_submit_application.png') ?>" alt="Confirm Submission" class="img-fluid rounded shadow">
                                    <p class="text-muted text-center mt-2"><small>Step 4: Confirm and submit application</small></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 10: Application Tracking -->
                <div class="card mb-5 border-0 shadow">
                    <div class="card-header bg-red text-white">
                        <h3 class="card-title mb-0"><i class="fas fa-list-alt me-2"></i>Step 10: Application Tracking</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-lg-8">
                                <h4>Track Your Applications</h4>
                                <p>After submitting your application, you can monitor its progress through the Applications section of your dashboard.</p>

                                <h5>View Your Applications</h5>
                                <ol>
                                    <li><strong>Navigate to Applications:</strong> Click "Applications" in main menu</li>
                                    <li><strong>Application List:</strong> See all your submitted applications</li>
                                    <li><strong>Application Details:</strong> Click to view specific application information</li>
                                    <li><strong>Status Tracking:</strong> Monitor application progress</li>
                                </ol>

                                <h5>Application Information</h5>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-hashtag text-primary me-2"></i><strong>Application Number:</strong> Unique identifier</li>
                                    <li><i class="fas fa-briefcase text-info me-2"></i><strong>Position Applied For:</strong> Job title and organization</li>
                                    <li><i class="fas fa-calendar text-success me-2"></i><strong>Submission Date:</strong> When you applied</li>
                                    <li><i class="fas fa-chart-line text-warning me-2"></i><strong>Application Status:</strong> Current stage in process</li>
                                    <li><i class="fas fa-file-alt text-secondary me-2"></i><strong>Documents Submitted:</strong> List of files included</li>
                                </ul>

                                <h5>Email Notifications</h5>
                                <ul>
                                    <li><strong>Application Receipt:</strong> Immediate confirmation email</li>
                                    <li><strong>Status Updates:</strong> Notifications of application progress</li>
                                    <li><strong>Interview Invitations:</strong> If selected for interview</li>
                                    <li><strong>Final Decisions:</strong> Outcome notifications</li>
                                </ul>

                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>Important:</strong> Keep your application numbers and emails for future reference. Check your email regularly for updates from the hiring organization.
                                </div>
                            </div>
                            <div class="col-lg-4">
                                <img src="<?= base_url('public/assets/how_to/applicant_my_applicantion.png') ?>" alt="Application Tracking" class="img-fluid rounded shadow">
                                <p class="text-muted text-center mt-2"><small>Your applications tracking interface</small></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Password Recovery -->
                <div class="card mb-5 border-0 shadow">
                    <div class="card-header bg-red text-white">
                        <h3 class="card-title mb-0"><i class="fas fa-key me-2"></i>Password Recovery</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-lg-8">
                                <h4>Forgot Password Process</h4>
                                <ol>
                                    <li><strong>Access Login Page:</strong> Go to applicant login</li>
                                    <li><strong>Click "Forgot Password":</strong> Link below login form</li>
                                    <li><strong>Enter Email:</strong> Provide your registered email address</li>
                                    <li><strong>Submit Request:</strong> Click submit button</li>
                                </ol>

                                <h5>Password Reset</h5>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-random text-primary me-2"></i><strong>New Password Generation:</strong> System creates 4-digit password</li>
                                    <li><i class="fas fa-envelope text-info me-2"></i><strong>Email Delivery:</strong> New password sent to your email</li>
                                    <li><i class="fas fa-check text-success me-2"></i><strong>Account Activation:</strong> Inactive accounts are automatically activated</li>
                                    <li><i class="fas fa-sign-in-alt text-warning me-2"></i><strong>Login with New Password:</strong> Use the 4-digit code to login</li>
                                    <li><i class="fas fa-edit text-secondary me-2"></i><strong>Change Password:</strong> Recommended to change after login</li>
                                </ul>

                                <div class="alert alert-warning">
                                    <i class="fas fa-shield-alt me-2"></i>
                                    <strong>Security Note:</strong> For security reasons, change your password immediately after logging in with the temporary 4-digit code.
                                </div>
                            </div>
                            <div class="col-lg-4">
                                <img src="<?= base_url('public/assets/how_to/home_applicant_forgot_password.png') ?>" alt="Forgot Password" class="img-fluid rounded shadow">
                                <p class="text-muted text-center mt-2"><small>Forgot password interface</small></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Troubleshooting -->
                <div class="card mb-5 border-0 shadow">
                    <div class="card-header bg-red text-white">
                        <h3 class="card-title mb-0"><i class="fas fa-tools me-2"></i>Troubleshooting & Support</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-lg-6">
                                <h5>Common Issues</h5>

                                <h6>Registration Problems</h6>
                                <ul>
                                    <li>Email already exists: Use different email or recover existing account</li>
                                    <li>Validation errors: Check all fields meet minimum requirements</li>
                                    <li>Password mismatch: Ensure password and confirmation match</li>
                                </ul>

                                <h6>Login Issues</h6>
                                <ul>
                                    <li>Account not activated: Check email for activation link</li>
                                    <li>Invalid credentials: Verify email and password are correct</li>
                                    <li>Account locked: Contact system administrator</li>
                                </ul>
                            </div>
                            <div class="col-lg-6">
                                <h6>File Upload Problems</h6>
                                <ul>
                                    <li>File too large: Ensure file is under 25MB</li>
                                    <li>Unsupported format: Use PDF, JPG, JPEG, or PNG</li>
                                    <li>Upload timeout: Try smaller files or better internet connection</li>
                                    <li>AI processing failed: Try re-uploading the file or contact support</li>
                                </ul>

                                <h6>Application Issues</h6>
                                <ul>
                                    <li>Profile incomplete: Complete all mandatory fields</li>
                                    <li>No files uploaded: Upload at least one supporting document</li>
                                    <li>Application deadline passed: Cannot apply after closing date</li>
                                </ul>

                                
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action -->
<section class="py-5 bg-light">
    <div class="container text-center">
        <h2 class="h1 fw-bold text-navy mb-4">Ready to Start Your Application?</h2>
        <p class="lead text-muted mb-4">Join thousands of applicants who have successfully applied through DERS</p>
        <div class="d-flex gap-3 justify-content-center">
            <a href="<?= base_url('applicant/register') ?>" class="btn btn-red btn-lg">
                <i class="fas fa-user-plus me-2"></i>Create Account Now
            </a>
            <a href="<?= base_url('jobs') ?>" class="btn btn-outline-dark btn-lg">
                <i class="fas fa-search me-2"></i>Browse Available Positions
            </a>
        </div>
    </div>
</section>

<?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
.hero-section {
    background: linear-gradient(135deg, #F00F00 0%, #D00D00 50%, #000000 100%);
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-section .container {
    position: relative;
    z-index: 1;
}

.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15) !important;
}

.bg-red {
    background: linear-gradient(135deg, #F00F00 0%, #D00D00 100%);
}

.text-navy {
    color: #1a1a1a;
}

.btn-yellow {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    border: none;
    color: #000;
    font-weight: 600;
}

.btn-yellow:hover {
    background: linear-gradient(135deg, #FFA500 0%, #FF8C00 100%);
    color: #000;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
}

.alert {
    border: none;
    border-radius: 10px;
}

.alert-info {
    background: linear-gradient(135deg, #E3F2FD 0%, #BBDEFB 100%);
    color: #0D47A1;
}

.alert-warning {
    background: linear-gradient(135deg, #FFF3E0 0%, #FFE0B2 100%);
    color: #E65100;
}

.alert-success {
    background: linear-gradient(135deg, #E8F5E8 0%, #C8E6C9 100%);
    color: #1B5E20;
}

.card-header.bg-red {
    border-radius: 10px 10px 0 0 !important;
}

.card {
    border-radius: 10px;
    overflow: hidden;
}

.img-fluid {
    border-radius: 8px;
}

ol, ul {
    padding-left: 1.5rem;
}

ol li, ul li {
    margin-bottom: 0.5rem;
}

h4 {
    color: #1a1a1a;
    font-weight: 600;
    margin-bottom: 1rem;
}

h5 {
    color: #333;
    font-weight: 600;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
}
</style>
<?= $this->endSection() ?>
